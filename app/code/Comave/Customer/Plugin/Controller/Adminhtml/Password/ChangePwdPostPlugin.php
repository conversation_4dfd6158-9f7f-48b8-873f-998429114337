<?php
/**
 * Copyright © Commercial Avenue
 */
declare(strict_types=1);

namespace Comave\Customer\Plugin\Controller\Adminhtml\Password;

use GhoSter\ChangeCustomerPassword\Controller\Adminhtml\Password\ChangePwdPost;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\Exception\LocalizedException;
use Magento\Integration\Api\CustomerTokenServiceInterface;
use Psr\Log\LoggerInterface;

class ChangePwdPostPlugin
{
    /**
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Integration\Api\CustomerTokenServiceInterface $tokenService
     */
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly CustomerTokenServiceInterface $tokenService
    ) {
    }

    /**
     * @param \GhoSter\ChangeCustomerPassword\Controller\Adminhtml\Password\ChangePwdPost $subject
     * @param \Magento\Backend\Model\View\Result\Redirect $result
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function afterExecute(ChangePwdPost $subject, Redirect $result): Redirect
    {
        $customerId = (int)$subject->getRequest()->getPost('customer_id');
        try {
            if (!empty($customerId)) {
                $this->tokenService->revokeCustomerAccessToken($customerId);
            }
        } catch (LocalizedException $e) {
            $this->logger->warning($e->getMessage());
        }

        return $result;
    }
}
