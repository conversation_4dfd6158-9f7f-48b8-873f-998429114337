<?php

declare(strict_types=1);

namespace Comave\CustomerGraphQl\Model\Resolver;

use Comave\CustomerGraphQl\Model\Command\AvatarImagePathResolver;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class AvatarImage implements ResolverInterface
{
    /**
     * @param AvatarImagePathResolver $avatarImagePathResolver
     */
    public function __construct(private readonly AvatarImagePathResolver $avatarImagePathResolver)
    {
    }

    /**
     * @inheritDoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        $customer = $value['model'];

        if (!$customer instanceof CustomerInterface) {
            return null;
        }

        $profileImage = $customer->getCustomAttribute('profile_picture')?->getValue();

        return !empty($profileImage) ?
            $this->avatarImagePathResolver->resolve($profileImage) : null;
    }
}
