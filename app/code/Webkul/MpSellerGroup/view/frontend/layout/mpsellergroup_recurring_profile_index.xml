<?xml version="1.0"?>
<!--
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerGroup
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<update handle="customer_account"/>
	<head>
		<css src="Webkul_MpSellerGroup::css/style.css"/>
	</head>
	<body>
		<referenceBlock name="page.main.title">
			<action method="setPageTitle">
				<argument translate="true" name="title" xsi:type="string">Seller Group Recurring Profiles</argument>
			</action>
		</referenceBlock>
		<referenceContainer name="content">
			<block class="Webkul\MpSellerGroup\Block\RecurringProfile" name="mpsellergroup_recurring_profile_list" template="Webkul_MpSellerGroup::recurring/profilelist.phtml" ifconfig="mpsellergroup/general_settings/status" cacheable="false"></block>
		</referenceContainer>
	</body>
</page>